import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useAuthStore } from '../store/authStore';
import { usePermissions } from '../hooks/usePermissions';

// Screen imports
import LoadingScreen from '../screens/LoadingScreen';
import AuthNavigator from './AuthNavigator';
import PublicNavigator from './PublicNavigator';
import GuestNavigator from './GuestNavigator';
import { AdminNavigator } from './AdminNavigator';

const Stack = createNativeStackNavigator();

// Main Tab Navigator for authenticated users
function AuthenticatedNavigator() {
  const { isStaff } = usePermissions();

  if (isStaff) {
    return <AdminNavigator />;
  }

  return <GuestNavigator />;
}

// Root Navigator with Browse-First Flow
export const AppNavigator = () => {
  const { user, session, loading, initialize } = useAuthStore();
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    const initializeAuth = async () => {
      await initialize();
      setIsInitializing(false);
    };
    initializeAuth();
  }, []);

  if (isInitializing || loading) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {user && session ? (
          // User is authenticated - show authenticated screens
          <Stack.Screen name="Authenticated" component={AuthenticatedNavigator} />
        ) : (
          // User is not authenticated - show public browsing
          <>
            <Stack.Screen name="Public" component={PublicNavigator} />
            {/* Auth screens as modal */}
            <Stack.Screen
              name="Auth"
              component={AuthNavigator}
              options={{
                presentation: 'modal',
                headerShown: false,
              }}
            />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};
